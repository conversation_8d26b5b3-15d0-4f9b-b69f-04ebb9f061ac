// Skripter Extension - Content Script for Meeting Platforms
class SkripterContent {
    constructor() {
        this.isRecording = false;
        this.recordingStartTime = null;
        this.timerInterval = null;
        this.recordingIndicator = null;
        this.platform = this.detectPlatform();
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.createRecordingUI();
        this.checkRecordingStatus();
    }

    setupEventListeners() {
        // Listen for messages from background script
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true;
        });

        // Listen for page visibility changes
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });

        // Listen for page unload
        window.addEventListener('beforeunload', () => {
            this.handlePageUnload();
        });
    }

    detectPlatform() {
        const url = window.location.href;
        
        if (url.includes('meet.google.com')) {
            return 'google-meet';
        } else if (url.includes('zoom.us')) {
            return 'zoom';
        } else if (url.includes('teams.microsoft.com')) {
            return 'teams';
        }
        
        return 'unknown';
    }

    createRecordingUI() {
        // Create recording indicator
        this.recordingIndicator = document.createElement('div');
        this.recordingIndicator.id = 'skripter-recording-indicator';
        this.recordingIndicator.className = 'skripter-recording-indicator';
        this.recordingIndicator.innerHTML = `
            <div class="skripter-recording-dot"></div>
            <span class="skripter-recording-text">Recording</span>
            <span class="skripter-recording-time">00:00</span>
        `;
        this.recordingIndicator.style.display = 'none';

        // Add styles
        this.addStyles();

        // Insert into page based on platform
        this.insertRecordingUI();
    }

    insertRecordingUI() {
        switch (this.platform) {
            case 'google-meet':
                this.insertGoogleMeetUI();
                break;
            case 'zoom':
                this.insertZoomUI();
                break;
            case 'teams':
                this.insertTeamsUI();
                break;
            default:
                this.insertGenericUI();
        }
    }

    insertGoogleMeetUI() {
        // Wait for Google Meet to load
        const waitForMeet = setInterval(() => {
            const meetControls = document.querySelector('[data-is-muted]')?.parentElement;
            if (meetControls) {
                clearInterval(waitForMeet);
                meetControls.appendChild(this.recordingIndicator);
            }
        }, 1000);
    }

    insertZoomUI() {
        // Wait for Zoom to load
        const waitForZoom = setInterval(() => {
            const zoomControls = document.querySelector('.footer__button')?.parentElement;
            if (zoomControls) {
                clearInterval(waitForZoom);
                zoomControls.appendChild(this.recordingIndicator);
            }
        }, 1000);
    }

    insertTeamsUI() {
        // Wait for Teams to load
        const waitForTeams = setInterval(() => {
            const teamsControls = document.querySelector('[data-tid="call-controls"]');
            if (teamsControls) {
                clearInterval(waitForTeams);
                teamsControls.appendChild(this.recordingIndicator);
            }
        }, 1000);
    }

    insertGenericUI() {
        // Generic insertion for unknown platforms
        const body = document.body;
        if (body) {
            body.appendChild(this.recordingIndicator);
        }
    }

    addStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .skripter-recording-indicator {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px 12px;
                background: rgba(220, 38, 38, 0.9);
                color: white;
                border-radius: 20px;
                font-size: 14px;
                font-weight: 500;
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                backdrop-filter: blur(10px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                animation: skripter-slide-in 0.3s ease-out;
            }

            .skripter-recording-dot {
                width: 8px;
                height: 8px;
                background: white;
                border-radius: 50%;
                animation: skripter-pulse 1s infinite;
            }

            .skripter-recording-text {
                font-weight: 600;
            }

            .skripter-recording-time {
                font-family: monospace;
                font-weight: 600;
            }

            @keyframes skripter-pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }

            @keyframes skripter-slide-in {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            .skripter-recording-indicator.hidden {
                display: none;
            }

            /* Platform-specific adjustments */
            .skripter-recording-indicator.google-meet {
                position: relative;
                top: auto;
                right: auto;
            }

            .skripter-recording-indicator.zoom {
                position: relative;
                top: auto;
                right: auto;
            }

            .skripter-recording-indicator.teams {
                position: relative;
                top: auto;
                right: auto;
            }
        `;
        document.head.appendChild(style);
    }

    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.type) {
                case 'RECORDING_STARTED':
                    this.startRecording(message.recordingTime);
                    sendResponse({ success: true });
                    break;

                case 'RECORDING_STOPPED':
                    this.stopRecording();
                    sendResponse({ success: true });
                    break;

                case 'GET_RECORDING_STATUS':
                    sendResponse({
                        isRecording: this.isRecording,
                        recordingTime: this.getRecordingTime()
                    });
                    break;

                default:
                    sendResponse({ error: 'Unknown message type' });
            }
        } catch (error) {
            console.error('Content script message handling error:', error);
            sendResponse({ error: error.message });
        }
    }

    startRecording(startTime = 0) {
        this.isRecording = true;
        this.recordingStartTime = Date.now() - startTime;
        this.showRecordingIndicator();
        this.startTimer();
    }

    stopRecording() {
        this.isRecording = false;
        this.hideRecordingIndicator();
        this.stopTimer();
    }

    showRecordingIndicator() {
        if (this.recordingIndicator) {
            this.recordingIndicator.style.display = 'flex';
            this.recordingIndicator.classList.add(this.platform);
        }
    }

    hideRecordingIndicator() {
        if (this.recordingIndicator) {
            this.recordingIndicator.style.display = 'none';
        }
    }

    startTimer() {
        this.timerInterval = setInterval(() => {
            this.updateTimer();
        }, 1000);
    }

    stopTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
    }

    updateTimer() {
        if (this.recordingIndicator && this.isRecording) {
            const elapsed = this.getRecordingTime();
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            const timeText = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            
            const timeElement = this.recordingIndicator.querySelector('.skripter-recording-time');
            if (timeElement) {
                timeElement.textContent = timeText;
            }
        }
    }

    getRecordingTime() {
        if (!this.recordingStartTime) return 0;
        return Date.now() - this.recordingStartTime;
    }

    async checkRecordingStatus() {
        try {
            const response = await chrome.runtime.sendMessage({
                type: 'GET_RECORDING_STATUS'
            });

            if (response.isRecording) {
                this.startRecording(response.recordingTime);
            }
        } catch (error) {
            console.error('Error checking recording status:', error);
        }
    }

    handleVisibilityChange() {
        if (document.hidden) {
            // Page is hidden, pause timer if recording
            if (this.isRecording && this.timerInterval) {
                clearInterval(this.timerInterval);
                this.timerInterval = null;
            }
        } else {
            // Page is visible again, resume timer if recording
            if (this.isRecording && !this.timerInterval) {
                this.startTimer();
            }
        }
    }

    handlePageUnload() {
        // Clean up when page is unloaded
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
        }
    }

    // Platform-specific methods
    getGoogleMeetInfo() {
        const participants = document.querySelectorAll('[data-participant-id]');
        const meetingTitle = document.querySelector('[data-meeting-title]')?.textContent || 'Google Meet';
        
        return {
            platform: 'google-meet',
            participants: participants.length,
            title: meetingTitle
        };
    }

    getZoomInfo() {
        const participants = document.querySelectorAll('.participant-item');
        const meetingTitle = document.querySelector('.meeting-title')?.textContent || 'Zoom Meeting';
        
        return {
            platform: 'zoom',
            participants: participants.length,
            title: meetingTitle
        };
    }

    getTeamsInfo() {
        const participants = document.querySelectorAll('[data-tid="participant-item"]');
        const meetingTitle = document.querySelector('[data-tid="meeting-title"]')?.textContent || 'Teams Meeting';
        
        return {
            platform: 'teams',
            participants: participants.length,
            title: meetingTitle
        };
    }

    getMeetingInfo() {
        switch (this.platform) {
            case 'google-meet':
                return this.getGoogleMeetInfo();
            case 'zoom':
                return this.getZoomInfo();
            case 'teams':
                return this.getTeamsInfo();
            default:
                return {
                    platform: 'unknown',
                    participants: 0,
                    title: 'Meeting'
                };
        }
    }
}

// Initialize content script
const content = new SkripterContent();

// Handle page load completion
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        // Content script is already initialized
    });
} else {
    // Page is already loaded
} 