<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Skripter Test</title>
    <link rel="stylesheet" href="styles/popup.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            width: 400px;
            height: 600px;
            margin: 20px;
        }
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #f0f0f0;
            padding: 10px;
            border: 1px solid #ccc;
            font-size: 12px;
            max-width: 200px;
        }
    </style>
</head>
<body>
    <div class="debug-info">
        <h4>Debug Info</h4>
        <div id="debugLog"></div>
    </div>

    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <img src="icons/icon32.png" alt="Skripter" class="logo-icon">
                <h1>Skripter</h1>
            </div>
            <button class="settings-btn" id="settingsBtn">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/>
                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1Z"/>
                </svg>
            </button>
        </header>

        <!-- Main App Section (Skip auth for testing) -->
        <div id="mainApp" class="main-app">
            <!-- Navigation -->
            <nav class="nav">
                <button class="nav-btn active" data-tab="record">Record</button>
                <button class="nav-btn" data-tab="upload">Upload</button>
                <button class="nav-btn" data-tab="transcripts">Transcripts</button>
            </nav>

            <!-- Record Tab -->
            <div id="recordTab" class="tab-content active">
                <div class="record-container">
                    <div class="record-status">
                        <div class="status-indicator" id="statusIndicator"></div>
                        <span id="statusText">Ready to record</span>
                    </div>
                    
                    <div class="record-controls">
                        <button id="recordBtn" class="btn btn-record">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                                <circle cx="12" cy="12" r="3"/>
                            </svg>
                            Start Recording
                        </button>
                        
                        <div class="recording-controls hidden" id="recordingControls">
                            <button id="pauseBtn" class="btn btn-secondary">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="6" y="4" width="4" height="16"/>
                                    <rect x="14" y="4" width="4" height="16"/>
                                </svg>
                                Pause
                            </button>
                            <button id="stopBtn" class="btn btn-danger">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="6" y="6" width="12" height="12"/>
                                </svg>
                                Stop
                            </button>
                        </div>
                    </div>
                    
                    <div class="recording-info hidden" id="recordingInfo">
                        <div class="timer" id="timer">00:00</div>
                        <div class="audio-visualizer" id="audioVisualizer"></div>
                    </div>
                </div>
            </div>

            <!-- Upload Tab -->
            <div id="uploadTab" class="tab-content">
                <div class="upload-container">
                    <div class="upload-area" id="uploadArea">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                            <polyline points="7,10 12,15 17,10"/>
                            <line x1="12" y1="15" x2="12" y2="3"/>
                        </svg>
                        <h3>Upload Audio File</h3>
                        <p>Drag and drop or click to select</p>
                        <p class="file-info">Supported: MP3, WAV, M4A (Max 10GB)</p>
                        <input type="file" id="fileInput" accept=".mp3,.wav,.m4a,.aac,.flac" hidden>
                    </div>
                    
                    <div class="upload-progress hidden" id="uploadProgress">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <span id="progressText">Uploading... 0%</span>
                    </div>
                </div>
            </div>

            <!-- Transcripts Tab -->
            <div id="transcriptsTab" class="tab-content">
                <div class="transcripts-container">
                    <div class="transcripts-header">
                        <h3>My Transcripts</h3>
                        <div class="search-box">
                            <input type="text" id="searchTranscripts" placeholder="Search transcripts...">
                        </div>
                    </div>
                    
                    <div class="transcripts-list" id="transcriptsList">
                        <!-- Transcripts will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Mock Chrome extension APIs for testing
        window.chrome = window.chrome || {
            runtime: {
                sendMessage: (message) => Promise.resolve({ success: false, error: 'Mock API' }),
                onMessage: { addListener: () => {} }
            },
            storage: {
                sync: {
                    get: () => Promise.resolve({}),
                    set: () => Promise.resolve(),
                    remove: () => Promise.resolve()
                },
                onChanged: { addListener: () => {} }
            }
        };

        // Debug logging
        const debugLog = document.getElementById('debugLog');
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        
        console.log = (...args) => {
            originalConsoleLog(...args);
            debugLog.innerHTML += '<div style="color: blue;">' + args.join(' ') + '</div>';
            debugLog.scrollTop = debugLog.scrollHeight;
        };
        
        console.error = (...args) => {
            originalConsoleError(...args);
            debugLog.innerHTML += '<div style="color: red;">' + args.join(' ') + '</div>';
            debugLog.scrollTop = debugLog.scrollHeight;
        };
    </script>
    <script src="scripts/popup.js"></script>
</body>
</html>
