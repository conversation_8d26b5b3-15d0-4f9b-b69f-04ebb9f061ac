{"name": "skripter-extension", "version": "1.0.0", "description": "A simple and intuitive Chrome/Edge extension for uploading, recording, and transcribing audio with features for generating summaries and reports.", "main": "background.js", "scripts": {"build": "echo 'No build process required for this extension'", "dev": "echo 'Load extension in browser for development'", "test": "echo 'No tests specified'", "lint": "echo 'No linter configured'", "package": "zip -r skripter-extension.zip . -x '*.git*' 'node_modules/*' '*.DS_Store'"}, "keywords": ["chrome-extension", "edge-extension", "transcription", "audio-recording", "meeting-recording", "google-meet", "zoom", "teams", "dutch", "nlp", "speech-to-text"], "author": {"name": "Skripter Team", "email": "<EMAIL>", "url": "https://skripter.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-repo/skripter-extension.git"}, "bugs": {"url": "https://github.com/your-repo/skripter-extension/issues"}, "homepage": "https://skripter.com", "engines": {"node": ">=14.0.0"}, "browserslist": ["Chrome >= 88", "Edge >= 88"], "devDependencies": {}, "dependencies": {}, "manifest": {"version": "1.0.0", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "A simple and intuitive tool for uploading, recording, and transcribing audio with summaries and reports"}}