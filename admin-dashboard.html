<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Skripter Admin Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
        }

        .admin-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 280px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            padding: 24px;
        }

        .sidebar-header {
            margin-bottom: 32px;
        }

        .sidebar-header h1 {
            font-size: 24px;
            font-weight: 600;
            color: #2563eb;
            margin-bottom: 8px;
        }

        .sidebar-header p {
            color: #6b7280;
            font-size: 14px;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 8px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            color: #374151;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .nav-link:hover {
            background: #f3f4f6;
        }

        .nav-link.active {
            background: #2563eb;
            color: white;
        }

        .nav-link svg {
            width: 20px;
            height: 20px;
        }

        .main-content {
            flex: 1;
            padding: 32px;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-header h2 {
            font-size: 32px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-header p {
            color: #6b7280;
            font-size: 16px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: #ffffff;
            border-radius: 12px;
            padding: 24px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .stat-card h3 {
            font-size: 14px;
            font-weight: 500;
            color: #6b7280;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-card .value {
            font-size: 32px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .stat-card .change {
            font-size: 14px;
            color: #10b981;
        }

        .stat-card .change.negative {
            color: #ef4444;
        }

        .content-section {
            background: #ffffff;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
        }

        .section-header {
            padding: 24px;
            border-bottom: 1px solid #e5e7eb;
        }

        .section-header h3 {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
        }

        .section-content {
            padding: 24px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .table th {
            font-weight: 600;
            color: #374151;
            background: #f9fafb;
        }

        .table tr:hover {
            background: #f9fafb;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .btn-primary {
            background: #2563eb;
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        .btn-danger {
            background: #dc2626;
            color: white;
        }

        .btn-danger:hover {
            background: #b91c1c;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-inactive {
            background: #fef2f2;
            color: #991b1b;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .search-box {
            display: flex;
            gap: 12px;
            margin-bottom: 24px;
        }

        .search-box input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }

        .search-box input:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            margin-top: 24px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
        }

        .pagination button.active {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .admin-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                border-right: none;
                border-bottom: 1px solid #e5e7eb;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h1>Skripter Admin</h1>
                <p>Platform Management Dashboard</p>
            </div>
            
            <nav class="nav-menu">
                <div class="nav-item">
                    <a href="#dashboard" class="nav-link active" data-page="dashboard">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="3" width="7" height="7"/>
                            <rect x="14" y="3" width="7" height="7"/>
                            <rect x="14" y="14" width="7" height="7"/>
                            <rect x="3" y="14" width="7" height="7"/>
                        </svg>
                        Dashboard
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#users" class="nav-link" data-page="users">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                            <circle cx="9" cy="7" r="4"/>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                        </svg>
                        Users
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#transcripts" class="nav-link" data-page="transcripts">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                            <polyline points="14,2 14,8 20,8"/>
                            <line x1="16" y1="13" x2="8" y2="13"/>
                            <line x1="16" y1="17" x2="8" y2="17"/>
                            <polyline points="10,9 9,9 8,9"/>
                        </svg>
                        Transcripts
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#analytics" class="nav-link" data-page="analytics">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="18" y1="20" x2="18" y2="10"/>
                            <line x1="12" y1="20" x2="12" y2="4"/>
                            <line x1="6" y1="20" x2="6" y2="14"/>
                        </svg>
                        Analytics
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#settings" class="nav-link" data-page="settings">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"/>
                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                        </svg>
                        Settings
                    </a>
                </div>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Dashboard Page -->
            <div id="dashboard-page" class="page-content">
                <div class="page-header">
                    <h2>Dashboard</h2>
                    <p>Overview of platform usage and statistics</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Total Users</h3>
                        <div class="value">1,247</div>
                        <div class="change">+12% from last month</div>
                    </div>
                    <div class="stat-card">
                        <h3>Active Subscriptions</h3>
                        <div class="value">892</div>
                        <div class="change">+8% from last month</div>
                    </div>
                    <div class="stat-card">
                        <h3>Total Transcripts</h3>
                        <div class="value">5,634</div>
                        <div class="change">+23% from last month</div>
                    </div>
                    <div class="stat-card">
                        <h3>Revenue (Monthly)</h3>
                        <div class="value">€12,450</div>
                        <div class="change">+15% from last month</div>
                    </div>
                </div>

                <div class="content-section">
                    <div class="section-header">
                        <h3>Recent Activity</h3>
                    </div>
                    <div class="section-content">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Action</th>
                                    <th>Platform</th>
                                    <th>Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><EMAIL></td>
                                    <td>Created transcript</td>
                                    <td>Google Meet</td>
                                    <td>2 minutes ago</td>
                                </tr>
                                <tr>
                                    <td><EMAIL></td>
                                    <td>Upgraded to Teams plan</td>
                                    <td>Web</td>
                                    <td>15 minutes ago</td>
                                </tr>
                                <tr>
                                    <td><EMAIL></td>
                                    <td>Uploaded audio file</td>
                                    <td>Upload</td>
                                    <td>1 hour ago</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Users Page -->
            <div id="users-page" class="page-content hidden">
                <div class="page-header">
                    <h2>User Management</h2>
                    <p>Manage user accounts and subscriptions</p>
                </div>

                <div class="search-box">
                    <input type="text" placeholder="Search users by name or email...">
                    <button class="btn btn-primary">Search</button>
                    <button class="btn btn-secondary">Export</button>
                </div>

                <div class="content-section">
                    <div class="section-header">
                        <h3>Users</h3>
                    </div>
                    <div class="section-content">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Plan</th>
                                    <th>Status</th>
                                    <th>Joined</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>John Doe</td>
                                    <td><EMAIL></td>
                                    <td>Teams</td>
                                    <td><span class="status-badge status-active">Active</span></td>
                                    <td>2024-01-15</td>
                                    <td>
                                        <button class="btn btn-secondary">Edit</button>
                                        <button class="btn btn-danger">Suspend</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Jane Smith</td>
                                    <td><EMAIL></td>
                                    <td>Basis</td>
                                    <td><span class="status-badge status-active">Active</span></td>
                                    <td>2024-01-10</td>
                                    <td>
                                        <button class="btn btn-secondary">Edit</button>
                                        <button class="btn btn-danger">Suspend</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Mike Wilson</td>
                                    <td><EMAIL></td>
                                    <td>Free</td>
                                    <td><span class="status-badge status-pending">Pending</span></td>
                                    <td>2024-01-20</td>
                                    <td>
                                        <button class="btn btn-secondary">Edit</button>
                                        <button class="btn btn-danger">Suspend</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <div class="pagination">
                            <button>Previous</button>
                            <button class="active">1</button>
                            <button>2</button>
                            <button>3</button>
                            <button>Next</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Transcripts Page -->
            <div id="transcripts-page" class="page-content hidden">
                <div class="page-header">
                    <h2>Transcripts</h2>
                    <p>View and manage all transcriptions</p>
                </div>

                <div class="search-box">
                    <input type="text" placeholder="Search transcripts...">
                    <button class="btn btn-primary">Search</button>
                    <button class="btn btn-secondary">Filter</button>
                </div>

                <div class="content-section">
                    <div class="section-header">
                        <h3>All Transcripts</h3>
                    </div>
                    <div class="section-content">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>User</th>
                                    <th>Platform</th>
                                    <th>Duration</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Team Meeting - Q1 Review</td>
                                    <td><EMAIL></td>
                                    <td>Google Meet</td>
                                    <td>45:30</td>
                                    <td><span class="status-badge status-active">Completed</span></td>
                                    <td>2024-01-20</td>
                                    <td>
                                        <button class="btn btn-secondary">View</button>
                                        <button class="btn btn-danger">Delete</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Client Presentation</td>
                                    <td><EMAIL></td>
                                    <td>Zoom</td>
                                    <td>32:15</td>
                                    <td><span class="status-badge status-active">Completed</span></td>
                                    <td>2024-01-19</td>
                                    <td>
                                        <button class="btn btn-secondary">View</button>
                                        <button class="btn btn-danger">Delete</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Interview Recording</td>
                                    <td><EMAIL></td>
                                    <td>Upload</td>
                                    <td>28:45</td>
                                    <td><span class="status-badge status-pending">Processing</span></td>
                                    <td>2024-01-20</td>
                                    <td>
                                        <button class="btn btn-secondary">View</button>
                                        <button class="btn btn-danger">Delete</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Analytics Page -->
            <div id="analytics-page" class="page-content hidden">
                <div class="page-header">
                    <h2>Analytics</h2>
                    <p>Platform usage statistics and insights</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Average Session Duration</h3>
                        <div class="value">24:30</div>
                        <div class="change">+5% from last month</div>
                    </div>
                    <div class="stat-card">
                        <h3>Transcription Accuracy</h3>
                        <div class="value">94.2%</div>
                        <div class="change">+1.2% from last month</div>
                    </div>
                    <div class="stat-card">
                        <h3>Most Used Platform</h3>
                        <div class="value">Google Meet</div>
                        <div class="change">45% of recordings</div>
                    </div>
                    <div class="stat-card">
                        <h3>Customer Satisfaction</h3>
                        <div class="value">4.8/5</div>
                        <div class="change">+0.2 from last month</div>
                    </div>
                </div>

                <div class="content-section">
                    <div class="section-header">
                        <h3>Usage Trends</h3>
                    </div>
                    <div class="section-content">
                        <p>Chart placeholder - Integration with Chart.js or similar library</p>
                    </div>
                </div>
            </div>

            <!-- Settings Page -->
            <div id="settings-page" class="page-content hidden">
                <div class="page-header">
                    <h2>Settings</h2>
                    <p>Configure platform settings and preferences</p>
                </div>

                <div class="content-section">
                    <div class="section-header">
                        <h3>Platform Configuration</h3>
                    </div>
                    <div class="section-content">
                        <div style="margin-bottom: 24px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 500;">API Base URL</label>
                            <input type="text" value="https://api.skripter.com" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;">
                        </div>
                        <div style="margin-bottom: 24px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 500;">Stripe Public Key</label>
                            <input type="text" value="pk_test_..." style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;">
                        </div>
                        <div style="margin-bottom: 24px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 500;">Google Client ID</label>
                            <input type="text" value="your-google-client-id.apps.googleusercontent.com" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;">
                        </div>
                        <button class="btn btn-primary">Save Settings</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simple navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                
                // Remove active class from all links
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                
                // Add active class to clicked link
                link.classList.add('active');
                
                // Hide all pages
                document.querySelectorAll('.page-content').forEach(page => {
                    page.classList.add('hidden');
                });
                
                // Show selected page
                const pageId = link.dataset.page + '-page';
                document.getElementById(pageId).classList.remove('hidden');
            });
        });
    </script>
</body>
</html> 