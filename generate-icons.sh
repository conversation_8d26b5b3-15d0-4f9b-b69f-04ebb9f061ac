#!/bin/bash

# Skripter Extension - Icon Generation Script
# This script generates PNG icons from the SVG files

echo "🎨 Generating Skripter Extension Icons..."

# Check if ImageMagick is installed
if ! command -v convert &> /dev/null; then
    echo "❌ ImageMagick is not installed. Please install it first:"
    echo "   macOS: brew install imagemagick"
    echo "   Ubuntu: sudo apt-get install imagemagick"
    echo "   Windows: Download from https://imagemagick.org/"
    exit 1
fi

# Create icons directory if it doesn't exist
mkdir -p icons

# Generate PNG icons from main SVG
echo "📱 Generating icon16.png (16x16)..."
convert icons/icon.svg -resize 16x16 icons/icon16.png

echo "📱 Generating icon32.png (32x32)..."
convert icons/icon.svg -resize 32x32 icons/icon32.png

echo "📱 Generating icon48.png (48x48)..."
convert icons/icon.svg -resize 48x48 icons/icon48.png

echo "📱 Generating icon128.png (128x128)..."
convert icons/icon.svg -resize 128x128 icons/icon128.png

# Generate recording state icon from recording SVG
echo "🔴 Generating icon-recording.png (32x32, red variant)..."
convert icons/icon-recording.svg -resize 32x32 icons/icon-recording.png

echo "✅ All icons generated successfully!"
echo ""
echo "📁 Generated files:"
ls -la icons/*.png
echo ""
echo "🎯 Icon sizes created:"
echo "   - icon16.png (16x16) - Extension toolbar"
echo "   - icon32.png (32x32) - Extension management"
echo "   - icon48.png (48x48) - Extension details"
echo "   - icon128.png (128x128) - Chrome Web Store"
echo "   - icon-recording.png (32x32) - Recording state (red variant)"
echo ""
echo "🎨 Icon Design:"
echo "   - Purple background (#8B5CF6) with white 'S'"
echo "   - Recording state: Red background (#DC2626) with indicator dot"
echo "   - Clean, minimalist design matching your brand"
echo ""
echo "🚀 Your extension is now ready with all required icons!" 