/* Skripter Content Script Styles */

/* Recording Indicator */
.skripter-recording-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(220, 38, 38, 0.95);
    color: white;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 999999;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: skripter-slide-in 0.3s ease-out;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.skripter-recording-dot {
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
    animation: skripter-pulse 1s infinite;
    flex-shrink: 0;
}

.skripter-recording-text {
    font-weight: 600;
    white-space: nowrap;
}

.skripter-recording-time {
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', <PERSON>solas, 'Courier New', monospace;
    font-weight: 600;
    white-space: nowrap;
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
}

/* Animations */
@keyframes skripter-pulse {
    0%, 100% { 
        opacity: 1; 
        transform: scale(1);
    }
    50% { 
        opacity: 0.7; 
        transform: scale(1.1);
    }
}

@keyframes skripter-slide-in {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes skripter-slide-out {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Platform-specific adjustments */

/* Google Meet */
.skripter-recording-indicator.google-meet {
    position: relative;
    top: auto;
    right: auto;
    margin: 8px;
    font-size: 13px;
}

/* Zoom */
.skripter-recording-indicator.zoom {
    position: relative;
    top: auto;
    right: auto;
    margin: 8px;
    font-size: 13px;
}

/* Teams */
.skripter-recording-indicator.teams {
    position: relative;
    top: auto;
    right: auto;
    margin: 8px;
    font-size: 13px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .skripter-recording-indicator {
        font-size: 12px;
        padding: 6px 10px;
        gap: 6px;
    }
    
    .skripter-recording-time {
        font-size: 11px;
        padding: 1px 4px;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .skripter-recording-indicator {
        background: #dc2626;
        border: 2px solid white;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .skripter-recording-dot {
        animation: none;
    }
    
    .skripter-recording-indicator {
        animation: none;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .skripter-recording-indicator {
        background: rgba(220, 38, 38, 0.98);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }
}

/* Hidden state */
.skripter-recording-indicator.hidden {
    display: none !important;
}

/* Hover effects */
.skripter-recording-indicator:hover {
    background: rgba(220, 38, 38, 1);
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

/* Focus styles for accessibility */
.skripter-recording-indicator:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .skripter-recording-indicator {
        display: none !important;
    }
} 