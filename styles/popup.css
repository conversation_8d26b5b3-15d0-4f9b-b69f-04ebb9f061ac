/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #ffffff;
    color: #333333;
    width: 400px;
    min-height: 600px;
    overflow-x: hidden;
}

.container {
    width: 100%;
    height: 100%;
}

/* Header Styles */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #ffffff;
    border-bottom: 1px solid #f0f0f0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    width: 32px;
    height: 32px;
}

.logo h1 {
    font-size: 24px;
    font-weight: 600;
    color: #2563eb;
}

.settings-btn {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.settings-btn:hover {
    background: #f3f4f6;
    color: #374151;
}

/* Auth Section Styles */
.auth-section {
    padding: 40px 20px;
    text-align: center;
}

.auth-container {
    max-width: 320px;
    margin: 0 auto;
}

.auth-container h2 {
    font-size: 28px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
}

.auth-container p {
    color: #6b7280;
    margin-bottom: 32px;
    font-size: 14px;
}

.auth-form {
    margin-bottom: 24px;
}

.form-group {
    margin-bottom: 16px;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.divider {
    position: relative;
    text-align: center;
    margin: 24px 0;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e5e7eb;
}

.divider span {
    background: #ffffff;
    padding: 0 16px;
    color: #6b7280;
    font-size: 14px;
}

.auth-footer {
    margin-top: 24px;
    font-size: 14px;
    color: #6b7280;
}

.auth-footer a {
    color: #2563eb;
    text-decoration: none;
    font-weight: 500;
}

.auth-footer a:hover {
    text-decoration: underline;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    width: 100%;
}

.btn-primary {
    background: #2563eb;
    color: #ffffff;
}

.btn-primary:hover {
    background: #1d4ed8;
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

.btn-google {
    background: #ffffff;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-google:hover {
    background: #f9fafb;
}

.btn-record {
    background: #dc2626;
    color: #ffffff;
    font-size: 16px;
    padding: 16px 32px;
}

.btn-record:hover {
    background: #b91c1c;
}

.btn-danger {
    background: #dc2626;
    color: #ffffff;
}

.btn-danger:hover {
    background: #b91c1c;
}

/* Main App Styles */
.main-app {
    height: calc(100vh - 80px);
    display: flex;
    flex-direction: column;
}

.nav {
    display: flex;
    background: #ffffff;
    border-bottom: 1px solid #f0f0f0;
}

.nav-btn {
    flex: 1;
    padding: 16px;
    background: none;
    border: none;
    color: #6b7280;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 2px solid transparent;
}

.nav-btn.active {
    color: #2563eb;
    border-bottom-color: #2563eb;
}

.nav-btn:hover {
    background: #f9fafb;
}

/* Tab Content Styles */
.tab-content {
    display: none;
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.tab-content.active {
    display: block;
}

/* Record Tab Styles */
.record-container {
    text-align: center;
}

.record-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 32px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #10b981;
}

.status-indicator.recording {
    background: #dc2626;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.record-controls {
    margin-bottom: 32px;
}

.recording-controls {
    display: flex;
    gap: 16px;
    justify-content: center;
}

.recording-controls .btn {
    width: auto;
    padding: 12px 20px;
}

.recording-info {
    margin-top: 24px;
}

.timer {
    font-size: 32px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 16px;
}

.audio-visualizer {
    height: 60px;
    background: #f3f4f6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
}

/* Upload Tab Styles */
.upload-container {
    text-align: center;
}

.upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    padding: 40px 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 24px;
}

.upload-area:hover {
    border-color: #2563eb;
    background: #f8fafc;
}

.upload-area.dragover {
    border-color: #2563eb;
    background: #eff6ff;
}

.upload-area svg {
    color: #6b7280;
    margin-bottom: 16px;
}

.upload-area h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
}

.upload-area p {
    color: #6b7280;
    margin-bottom: 4px;
}

.file-info {
    font-size: 12px;
    color: #9ca3af;
}

.upload-progress {
    margin-top: 24px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #f3f4f6;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: #2563eb;
    width: 0%;
    transition: width 0.3s ease;
}

/* Transcripts Tab Styles */
.transcripts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.transcripts-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
}

.search-box input {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    width: 200px;
}

.search-box input:focus {
    outline: none;
    border-color: #2563eb;
}

.transcripts-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.transcript-item {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.transcript-item:hover {
    border-color: #2563eb;
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.1);
}

.transcript-item h4 {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 4px;
}

.transcript-item p {
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 8px;
}

.transcript-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #9ca3af;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: #ffffff;
    border-radius: 12px;
    padding: 32px;
    max-width: 400px;
    width: 90%;
    text-align: center;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.modal-header h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
}

.close-btn:hover {
    color: #374151;
}

.processing-spinner {
    width: 48px;
    height: 48px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #2563eb;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 24px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.settings-content {
    text-align: left;
}

.setting-group {
    margin-bottom: 20px;
}

.setting-group label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
}

.setting-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    background: #ffffff;
}

.setting-group select:focus {
    outline: none;
    border-color: #2563eb;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Dark Mode Support */
[data-theme="dark"] {
    background: #1f2937;
    color: #f9fafb;
}

[data-theme="dark"] .header {
    background: #111827;
    border-bottom-color: #374151;
}

[data-theme="dark"] .logo h1 {
    color: #60a5fa;
}

[data-theme="dark"] .auth-container h2 {
    color: #f9fafb;
}

[data-theme="dark"] .auth-container p {
    color: #d1d5db;
}

[data-theme="dark"] .form-group input {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
}

[data-theme="dark"] .btn-secondary {
    background: #374151;
    color: #f9fafb;
    border-color: #4b5563;
}

[data-theme="dark"] .nav {
    background: #111827;
    border-bottom-color: #374151;
}

[data-theme="dark"] .nav-btn {
    color: #d1d5db;
}

[data-theme="dark"] .nav-btn:hover {
    background: #374151;
}

[data-theme="dark"] .transcript-item {
    background: #374151;
    border-color: #4b5563;
}

[data-theme="dark"] .transcript-item h4 {
    color: #f9fafb;
}

[data-theme="dark"] .transcript-item p {
    color: #d1d5db;
}

[data-theme="dark"] .modal-content {
    background: #374151;
}

[data-theme="dark"] .modal-header h3 {
    color: #f9fafb;
}

[data-theme="dark"] .setting-group select {
    background: #4b5563;
    border-color: #6b7280;
    color: #f9fafb;
} 