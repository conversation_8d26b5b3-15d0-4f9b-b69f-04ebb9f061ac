# 🎤 Microfoon Setup voor Skripter

## Probleem Oplossen: Microfoon Permissies

Als je de foutmelding "Could not start recording. Please check microphone permissions" krijgt, volg deze stappen:

### 🔧 **Stap 1: Browser Permissies Controleren**

#### **Chrome:**
1. Klik op het slotje 🔒 naast de URL in de adresbalk
2. <PERSON><PERSON> naar "Microfoon" of "Microphone"
3. <PERSON>org dat het ingesteld is op "Toestaan" of "Allow"
4. Herlaad de pagina

#### **Edge:**
1. Klik op het slotje 🔒 naast de URL
2. <PERSON><PERSON> naar "Microfoon" of "Microphone"
3. <PERSON>org dat het ingesteld is op "Toestaan" of "Allow"
4. Herlaad de pagina

### 🔧 **Stap 2: Systeem Permissies Controleren**

#### **macOS:**
1. Ga naar **Systeemvoorkeuren** > **Beveiliging en privacy** > **Privacy**
2. Selecteer **Microfoon** in de linker kolom
3. <PERSON>org dat je browser (Chrome/Edge) is aangevinkt
4. Herstart je browser

#### **Windows:**
1. Ga naar **Instellingen** > **Privacy** > **Microfoon**
2. Zorg dat "Microfoon toegang" is ingeschakeld
3. Controleer of je browser toegang heeft
4. Herstart je browser

### 🔧 **Stap 3: Extensie Permissies**

1. Ga naar `chrome://extensions/` (Chrome) of `edge://extensions/` (Edge)
2. Zoek de **Skripter** extensie
3. Klik op **Details**
4. Controleer of **Microfoon** permissie is ingeschakeld
5. Herlaad de extensie

### 🔧 **Stap 4: Test de Microfoon**

1. Open de Skripter extensie
2. Ga naar het **Record** tabblad
3. Klik op de **Record** knop
4. Je zou een popup moeten zien die vraagt om microfoon toegang
5. Klik **Toestaan** of **Allow**

### 🔧 **Stap 5: Problemen Oplossen**

#### **Als er nog steeds problemen zijn:**

1. **Controleer of je microfoon werkt:**
   - Test je microfoon in andere apps
   - Zorg dat je microfoon niet op mute staat

2. **Controleer browser instellingen:**
   - Ga naar browser instellingen
   - Zoek naar "Site settings" of "Site-instellingen"
   - Controleer microfoon permissies

3. **Herstart de browser:**
   - Sluit alle browser vensters
   - Start de browser opnieuw op
   - Probeer opnieuw

4. **Controleer andere apps:**
   - Zorg dat geen andere app je microfoon gebruikt
   - Sluit apps zoals Zoom, Teams, etc.

### 🔧 **Stap 6: Alternatieve Oplossingen**

#### **Als microfoon opname niet werkt:**
- Gebruik de **Upload** functie om audio bestanden te uploaden
- Probeer een andere browser (Chrome/Edge/Firefox)
- Controleer of je microfoon driver up-to-date is

### 📞 **Hulp Nodig?**

Als je nog steeds problemen hebt:
1. Controleer de browser console voor foutmeldingen
2. Probeer de extensie in incognito/private modus
3. Controleer of je antivirus software de microfoon blokkeert

---

## ✅ **Succesvolle Setup Checklist:**

- [ ] Browser microfoon permissie toegestaan
- [ ] Systeem microfoon permissie toegestaan  
- [ ] Extensie microfoon permissie toegestaan
- [ ] Microfoon werkt in andere apps
- [ ] Geen andere apps gebruiken de microfoon
- [ ] Extensie herladen na permissie wijzigingen

**🎯 Als alle stappen zijn gevolgd, zou de opname functie moeten werken!** 