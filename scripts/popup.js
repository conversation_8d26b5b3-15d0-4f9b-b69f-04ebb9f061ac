// Skripter Extension - Main Popup Script
class SkripterApp {
    constructor() {
        this.currentUser = null;
        this.isRecording = false;
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.recordingStartTime = null;
        this.timerInterval = null;
        this.currentTab = 'record';
        
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.checkAuthStatus();
        this.loadSettings();
    }

    setupEventListeners() {
        // Auth related events
        document.getElementById('loginForm').addEventListener('submit', (e) => this.handleLogin(e));
        document.getElementById('registerForm').addEventListener('submit', (e) => this.handleRegister(e));
        document.getElementById('googleSignIn').addEventListener('click', () => this.handleGoogleSignIn());
        document.getElementById('showRegister').addEventListener('click', (e) => this.showRegisterForm(e));
        document.getElementById('showLogin').addEventListener('click', (e) => this.showLoginForm(e));
        document.getElementById('logoutBtn').addEventListener('click', () => this.handleLogout());

        // Navigation events
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
        });

        // Recording events
        document.getElementById('recordBtn').addEventListener('click', () => this.toggleRecording());
        document.getElementById('pauseBtn').addEventListener('click', () => this.pauseRecording());
        document.getElementById('stopBtn').addEventListener('click', () => this.stopRecording());

        // Upload events
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        uploadArea.addEventListener('drop', (e) => this.handleFileDrop(e));
        fileInput.addEventListener('change', (e) => this.handleFileSelect(e));

        // Settings events
        document.getElementById('settingsBtn').addEventListener('click', () => this.showSettings());
        document.getElementById('closeSettings').addEventListener('click', () => this.hideSettings());
        document.getElementById('transcriptionLanguage').addEventListener('change', (e) => this.saveSettings());
        document.getElementById('transcriptionQuality').addEventListener('change', (e) => this.saveSettings());
        document.getElementById('themeSelect').addEventListener('change', (e) => this.changeTheme(e.target.value));

        // Search events
        document.getElementById('searchTranscripts').addEventListener('input', (e) => this.searchTranscripts(e.target.value));
    }

    async checkAuthStatus() {
        try {
            const user = await this.getStoredUser();
            if (user && user.token) {
                this.currentUser = user;
                this.showMainApp();
                this.loadTranscripts();
            } else {
                this.showAuthSection();
            }
        } catch (error) {
            console.error('Error checking auth status:', error);
            this.showAuthSection();
        }
    }

    async handleLogin(e) {
        e.preventDefault();
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;

        try {
            this.showLoading('Signing in...');
            
            // Mock authentication - simulate API delay
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // For demo purposes, accept any email/password combination
            const mockUser = {
                id: 'user_' + Date.now(),
                name: email.split('@')[0],
                email: email,
                token: 'mock_token_' + Date.now(),
                plan: 'free',
                createdAt: new Date().toISOString()
            };

            this.currentUser = mockUser;
            await this.storeUser(mockUser);
            this.showMainApp();
            this.loadTranscripts();
            this.hideLoading();
            this.showSuccess('Welcome back to Skripter!');
            
        } catch (error) {
            console.error('Login error:', error);
            this.hideLoading();
            this.showError('Login failed. Please try again.');
        }
    }

    async handleRegister(e) {
        e.preventDefault();
        const name = document.getElementById('regName').value;
        const email = document.getElementById('regEmail').value;
        const password = document.getElementById('regPassword').value;
        const confirmPassword = document.getElementById('regConfirmPassword').value;

        if (password !== confirmPassword) {
            this.showError('Passwords do not match');
            return;
        }

        if (password.length < 6) {
            this.showError('Password must be at least 6 characters long');
            return;
        }

        try {
            this.showLoading('Creating account...');
            
            // Mock registration - simulate API delay
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            // Create mock user
            const mockUser = {
                id: 'user_' + Date.now(),
                name: name,
                email: email,
                token: 'mock_token_' + Date.now(),
                plan: 'free',
                createdAt: new Date().toISOString()
            };

            this.currentUser = mockUser;
            await this.storeUser(mockUser);
            this.showMainApp();
            this.loadTranscripts();
            this.hideLoading();
            this.showSuccess('Account created successfully! Welcome to Skripter.');
            
        } catch (error) {
            console.error('Registration error:', error);
            this.hideLoading();
            this.showError('Registration failed. Please try again.');
        }
    }

    async handleGoogleSignIn() {
        try {
            this.showLoading('Signing in with Google...');
            
            // Mock Google sign-in - simulate API delay
            await new Promise(resolve => setTimeout(resolve, 1200));
            
            // Create mock user for Google sign-in
            const mockUser = {
                id: 'google_user_' + Date.now(),
                name: 'Google User',
                email: '<EMAIL>',
                token: 'google_mock_token_' + Date.now(),
                plan: 'free',
                createdAt: new Date().toISOString()
            };

            this.currentUser = mockUser;
            await this.storeUser(mockUser);
            this.showMainApp();
            this.loadTranscripts();
            this.hideLoading();
            this.showSuccess('Welcome to Skripter!');
            
        } catch (error) {
            console.error('Google sign-in error:', error);
            this.hideLoading();
            this.showError('Google sign-in failed. Please try again.');
        }
    }

    async handleLogout() {
        this.currentUser = null;
        await this.clearStoredUser();
        this.hideSettings();
        this.showAuthSection();
        this.showSuccess('You have been signed out.');
    }

    showRegisterForm(e) {
        e.preventDefault();
        document.getElementById('authSection').classList.add('hidden');
        document.getElementById('registerSection').classList.remove('hidden');
    }

    showLoginForm(e) {
        e.preventDefault();
        document.getElementById('registerSection').classList.add('hidden');
        document.getElementById('authSection').classList.remove('hidden');
    }

    showAuthSection() {
        document.getElementById('mainApp').classList.add('hidden');
        document.getElementById('registerSection').classList.add('hidden');
        document.getElementById('authSection').classList.remove('hidden');
    }

    showMainApp() {
        document.getElementById('authSection').classList.add('hidden');
        document.getElementById('registerSection').classList.add('hidden');
        document.getElementById('mainApp').classList.remove('hidden');
    }

    switchTab(tabName) {
        // Update navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update content
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        document.getElementById(`${tabName}Tab`).classList.add('active');

        this.currentTab = tabName;
    }

    async toggleRecording() {
        if (this.isRecording) {
            await this.pauseRecording();
        } else {
            // Try to start recording via background script first
            try {
                const response = await chrome.runtime.sendMessage({
                    action: 'startRecording'
                });

                if (response && response.success) {
                    this.isRecording = true;
                    this.recordingStartTime = Date.now();
                    this.startTimer();
                    this.updateRecordingUI();
                } else {
                    // Fallback to popup-based recording
                    await this.checkMicrophoneAccess();
                }
            } catch (error) {
                console.log('Background recording failed, trying popup recording:', error);
                // Fallback to popup-based recording
                await this.checkMicrophoneAccess();
            }
        }
    }

    async checkMicrophoneAccess() {
        try {
            // Check if mediaDevices is supported
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                this.showError('Audio recording is not supported in this browser.');
                return;
            }

            // First, try to get permission without constraints to check access
            try {
                const testStream = await navigator.mediaDevices.getUserMedia({ audio: true });
                testStream.getTracks().forEach(track => track.stop()); // Stop immediately
                console.log('Microphone access granted');
            } catch (permissionError) {
                if (permissionError.name === 'NotAllowedError') {
                    this.showDetailedPermissionError();
                    return;
                } else if (permissionError.name === 'NotFoundError') {
                    this.showError('No microphone found. Please connect a microphone and try again.');
                    return;
                } else {
                    throw permissionError;
                }
            }

            // Check if we can enumerate devices
            const devices = await navigator.mediaDevices.enumerateDevices();
            const audioDevices = devices.filter(device => device.kind === 'audioinput');

            if (audioDevices.length === 0) {
                this.showError('No microphone found. Please connect a microphone and try again.');
                return;
            }

            console.log('Available audio devices:', audioDevices.map(d => d.label || 'Unknown device'));

            // If we have devices, try to start recording
            await this.startRecording();

        } catch (error) {
            console.error('Error checking microphone access:', error);
            this.showError('Could not access microphone. Please check permissions and try again.');
        }
    }

    async startRecording() {
        try {
            // Check if mediaDevices is supported
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                throw new Error('Media devices not supported in this browser');
            }

            // Request microphone permission with specific constraints
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true,
                    sampleRate: 44100,
                    channelCount: 1
                }
            });

            // Check if we actually got audio tracks
            const audioTracks = stream.getAudioTracks();
            if (audioTracks.length === 0) {
                throw new Error('No audio tracks available');
            }

            this.mediaRecorder = new MediaRecorder(stream, {
                mimeType: 'audio/webm;codecs=opus'
            });
            this.audioChunks = [];

            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.audioChunks.push(event.data);
                }
            };

            this.mediaRecorder.onstop = async () => {
                if (this.audioChunks.length > 0) {
                    const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });
                    await this.processAudio(audioBlob, 'recording');
                } else {
                    this.showError('No audio data recorded. Please try again.');
                }
            };

            this.mediaRecorder.onerror = (event) => {
                console.error('MediaRecorder error:', event);
                this.showError('Recording error occurred. Please try again.');
                this.stopRecording();
            };

            this.mediaRecorder.start(1000); // Collect data every second
            this.isRecording = true;
            this.recordingStartTime = Date.now();
            this.startTimer();
            this.updateRecordingUI();
            
            console.log('Recording started successfully');
        } catch (error) {
            console.error('Error starting recording:', error);
            
            let errorMessage = 'Could not start recording. ';
            
            if (error.name === 'NotAllowedError') {
                errorMessage += 'Microphone permission denied. Please allow microphone access in your browser settings.';
            } else if (error.name === 'NotFoundError') {
                errorMessage += 'No microphone found. Please connect a microphone and try again.';
            } else if (error.name === 'NotSupportedError') {
                errorMessage += 'Audio recording not supported in this browser.';
            } else if (error.name === 'NotReadableError') {
                errorMessage += 'Microphone is already in use by another application.';
            } else {
                errorMessage += 'Please check microphone permissions and try again.';
            }
            
            this.showError(errorMessage);
        }
    }

    async pauseRecording() {
        if (this.mediaRecorder && this.isRecording) {
            this.mediaRecorder.pause();
            this.isRecording = false;
            this.stopTimer();
            this.updateRecordingUI();
        }
    }

    async stopRecording() {
        if (this.mediaRecorder && this.isRecording) {
            try {
                this.mediaRecorder.stop();
                this.isRecording = false;
                this.stopTimer();
                this.updateRecordingUI();
                
                // Stop all tracks
                if (this.mediaRecorder.stream) {
                    this.mediaRecorder.stream.getTracks().forEach(track => {
                        track.stop();
                        console.log('Audio track stopped:', track.label);
                    });
                }
                
                console.log('Recording stopped successfully');
            } catch (error) {
                console.error('Error stopping recording:', error);
                this.showError('Error stopping recording. Please try again.');
            }
        }
    }

    startTimer() {
        this.timerInterval = setInterval(() => {
            const elapsed = Date.now() - this.recordingStartTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            document.getElementById('timer').textContent = 
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }, 1000);
    }

    stopTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
    }

    updateRecordingUI() {
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        const recordBtn = document.getElementById('recordBtn');
        const recordingControls = document.getElementById('recordingControls');
        const recordingInfo = document.getElementById('recordingInfo');

        if (this.isRecording) {
            statusIndicator.classList.add('recording');
            statusText.textContent = 'Recording...';
            recordBtn.classList.add('hidden');
            recordingControls.classList.remove('hidden');
            recordingInfo.classList.remove('hidden');
        } else {
            statusIndicator.classList.remove('recording');
            statusText.textContent = 'Ready to record';
            recordBtn.classList.remove('hidden');
            recordingControls.classList.add('hidden');
            recordingInfo.classList.add('hidden');
        }
    }

    handleDragOver(e) {
        e.preventDefault();
        e.currentTarget.classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('dragover');
    }

    handleFileDrop(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.processFile(files[0]);
        }
    }

    handleFileSelect(e) {
        const file = e.target.files[0];
        if (file) {
            this.processFile(file);
        }
    }

    async processFile(file) {
        // Validate file type and size
        const allowedTypes = ['audio/mp3', 'audio/wav', 'audio/m4a', 'audio/aac', 'audio/flac'];
        const maxSize = 10 * 1024 * 1024 * 1024; // 10GB

        if (!allowedTypes.includes(file.type)) {
            this.showError('Unsupported file type. Please upload MP3, WAV, M4A, AAC, or FLAC files.');
            return;
        }

        if (file.size > maxSize) {
            this.showError('File size exceeds 10GB limit.');
            return;
        }

        await this.processAudio(file, 'upload');
    }

    async processAudio(audioBlob, source) {
        try {
            this.showProcessingModal('Processing Audio', 'Please wait while we transcribe your audio...');
            
            // Simulate processing delay
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // Create mock transcript
            const mockTranscript = {
                id: 'transcript_' + Date.now(),
                title: source === 'recording' ? 'Recording ' + new Date().toLocaleString() : 'Uploaded Audio',
                summary: 'This is a mock transcript for demonstration purposes. In a real implementation, this would contain the actual transcribed text from your audio.',
                duration: '00:45',
                createdAt: new Date().toISOString(),
                status: 'completed'
            };

            this.hideProcessingModal();
            this.showSuccess('Transcription completed! (Demo mode)');
            this.loadTranscripts();
            
        } catch (error) {
            console.error('Processing error:', error);
            this.hideProcessingModal();
            this.showError('Failed to process audio. Please try again.');
        }
    }

    async loadTranscripts() {
        try {
            // Load mock transcripts for demo
            const mockTranscripts = [
                {
                    id: 'transcript_1',
                    title: 'Team Meeting - Q1 Review',
                    summary: 'Discussion about quarterly goals and upcoming projects.',
                    duration: '45:30',
                    createdAt: '2024-01-20T10:00:00Z',
                    status: 'completed'
                },
                {
                    id: 'transcript_2',
                    title: 'Client Presentation',
                    summary: 'Product demonstration and feature overview for potential clients.',
                    duration: '32:15',
                    createdAt: '2024-01-19T14:30:00Z',
                    status: 'completed'
                },
                {
                    id: 'transcript_3',
                    title: 'Interview Recording',
                    summary: 'Candidate interview for senior developer position.',
                    duration: '28:45',
                    createdAt: '2024-01-18T09:15:00Z',
                    status: 'completed'
                }
            ];

            this.renderTranscripts(mockTranscripts);
        } catch (error) {
            console.error('Error loading transcripts:', error);
        }
    }

    renderTranscripts(transcripts) {
        const container = document.getElementById('transcriptsList');
        container.innerHTML = '';

        if (transcripts.length === 0) {
            container.innerHTML = '<p class="no-transcripts">No transcripts yet. Start recording or upload audio to get started!</p>';
            return;
        }

        transcripts.forEach(transcript => {
            const item = document.createElement('div');
            item.className = 'transcript-item';
            item.innerHTML = `
                <h4>${transcript.title}</h4>
                <p>${transcript.summary || 'No summary available'}</p>
                <div class="transcript-meta">
                    <span>${new Date(transcript.createdAt).toLocaleDateString()}</span>
                    <span>${transcript.duration || 'Unknown duration'}</span>
                </div>
            `;
            item.addEventListener('click', () => this.openTranscript(transcript.id));
            container.appendChild(item);
        });
    }

    async openTranscript(transcriptId) {
        // For demo purposes, show an alert
        alert(`Opening transcript ${transcriptId} (Demo mode - would open in full view)`);
    }

    searchTranscripts(query) {
        // Implement search functionality
        console.log('Searching for:', query);
    }

    showSettings() {
        document.getElementById('settingsModal').classList.remove('hidden');
    }

    hideSettings() {
        document.getElementById('settingsModal').classList.add('hidden');
    }

    async saveSettings() {
        const settings = {
            transcriptionLanguage: document.getElementById('transcriptionLanguage').value,
            transcriptionQuality: document.getElementById('transcriptionQuality').value,
            theme: document.getElementById('themeSelect').value
        };

        await chrome.storage.sync.set({ settings });
    }

    async loadSettings() {
        const result = await chrome.storage.sync.get(['settings']);
        const settings = result.settings || {};

        document.getElementById('transcriptionLanguage').value = settings.transcriptionLanguage || 'nl';
        document.getElementById('transcriptionQuality').value = settings.transcriptionQuality || 'quick';
        document.getElementById('themeSelect').value = settings.theme || 'light';

        this.changeTheme(settings.theme || 'light');
    }

    changeTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        this.saveSettings();
    }

    getSetting(key, defaultValue) {
        const element = document.getElementById(key);
        return element ? element.value : defaultValue;
    }

    showProcessingModal(title, text) {
        document.getElementById('processingTitle').textContent = title;
        document.getElementById('processingText').textContent = text;
        document.getElementById('processingModal').classList.remove('hidden');
    }

    hideProcessingModal() {
        document.getElementById('processingModal').classList.add('hidden');
    }

    showLoading(message) {
        this.showProcessingModal('Loading', message);
    }

    hideLoading() {
        this.hideProcessingModal();
    }

    showError(message) {
        // Implement error notification
        console.error(message);
        alert('Error: ' + message);
    }

    showDetailedPermissionError() {
        const errorMessage = `Microphone permission denied. To fix this:

1. Click the camera/microphone icon in your browser's address bar
2. Select "Always allow" for microphone access
3. Reload this extension
4. Try recording again

Alternative steps:
• Go to Chrome Settings > Privacy and Security > Site Settings > Microphone
• Make sure microphone access is allowed
• Restart Chrome and try again`;

        console.error('Microphone permission denied');
        alert(errorMessage);
    }

    showSuccess(message) {
        // Implement success notification
        console.log(message);
        alert('Success: ' + message);
    }

    async storeUser(user) {
        await chrome.storage.sync.set({ user });
    }

    async getStoredUser() {
        const result = await chrome.storage.sync.get(['user']);
        return result.user;
    }

    async clearStoredUser() {
        await chrome.storage.sync.remove(['user']);
    }
}

// Initialize the app when the popup loads
document.addEventListener('DOMContentLoaded', () => {
    new SkripterApp();
}); 