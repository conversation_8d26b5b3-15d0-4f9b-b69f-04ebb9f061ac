# Skripter Extension Installation Guide

## Quick Installation

### For Chrome Users

1. **Download the Extension**
   - Download all files from this repository
   - Extract to a folder on your computer

2. **Open Chrome Extensions**
   - Open Chrome browser
   - Go to `chrome://extensions/`
   - Enable "Developer mode" (toggle in top right corner)

3. **Load the Extension**
   - Click "Load unpacked"
   - Select the folder containing the extension files
   - The Skripter extension should now appear in your extensions list

4. **Pin the Extension**
   - Click the puzzle piece icon in Chrome toolbar
   - Find "Skripter" and click the pin icon
   - The extension icon will now appear in your toolbar

### For Edge Users

1. **Download the Extension**
   - Download all files from this repository
   - Extract to a folder on your computer

2. **Open Edge Extensions**
   - Open Edge browser
   - Go to `edge://extensions/`
   - Enable "Developer mode" (toggle in bottom left)

3. **Load the Extension**
   - Click "Load unpacked"
   - Select the folder containing the extension files
   - The Skripter extension should now appear in your extensions list

4. **Pin the Extension**
   - Click the puzzle piece icon in Edge toolbar
   - Find "Skripter" and click the pin icon
   - The extension icon will now appear in your toolbar

## First Time Setup

1. **Click the Extension Icon**
   - Click the Skripter icon in your browser toolbar
   - The extension popup will open

2. **Create an Account**
   - Click "Sign up" to create a new account
   - Enter your name, email, and password
   - Or click "Sign in with Google" to use your Google account

3. **Choose Your Plan**
   - Select the plan that fits your needs:
     - **Free**: 3 recordings/month, 1 hour max
     - **Basis**: Unlimited recordings, full features
     - **Teams**: Advanced features and priority support

4. **Start Using**
   - You can now record audio directly in the extension
   - Or use it in Google Meet, Zoom, and Microsoft Teams meetings

## Using the Extension

### Recording Audio
- **Direct Recording**: Click the extension icon → Record tab → Start Recording
- **Meeting Recording**: Right-click on Google Meet/Zoom/Teams pages → "Start Recording with Skripter"

### Uploading Files
- Click the extension icon → Upload tab
- Drag and drop audio files or click to select
- Supported formats: MP3, WAV, M4A, AAC, FLAC (up to 10GB)

### Managing Transcripts
- Click the extension icon → Transcripts tab
- View all your transcriptions
- Search and organize your content
- Export in DOCX or PDF format

## Troubleshooting

### Extension Not Loading
- Make sure all files are in the same folder
- Check that `manifest.json` is present
- Try refreshing the extensions page

### Recording Not Working
- Check microphone permissions in browser settings
- Ensure you're on an HTTPS page
- Try refreshing the page

### Meeting Integration Issues
- Make sure you're on a supported platform (Google Meet, Zoom, Teams)
- Refresh the meeting page
- Check that the extension is enabled

### Permission Issues
- Go to `chrome://settings/content/microphone` (Chrome)
- Go to `edge://settings/content/microphone` (Edge)
- Ensure Skripter has microphone permission

## Support

If you encounter any issues:
1. Check the browser console for error messages
2. Try reloading the extension
3. Contact <NAME_EMAIL>

## File Structure

Make sure your extension folder contains:
```
skripter-extension/
├── manifest.json
├── popup.html
├── background.js
├── content.js
├── content.css
├── styles/
│   └── popup.css
├── scripts/
│   └── popup.js
├── icons/
│   └── icon.svg
└── README.md
```

All files must be present for the extension to work properly. 