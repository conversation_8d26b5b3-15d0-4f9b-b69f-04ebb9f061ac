<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Skripter Icon Generator</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 32px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #8B5CF6;
            margin-bottom: 24px;
        }
        .icon-preview {
            display: flex;
            gap: 20px;
            margin: 24px 0;
            flex-wrap: wrap;
        }
        .icon-size {
            text-align: center;
            padding: 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }
        .icon-size h3 {
            margin-bottom: 8px;
            color: #374151;
        }
        .icon-display {
            border: 1px solid #d1d5db;
            border-radius: 4px;
            background: white;
            display: inline-block;
        }
        .instructions {
            background: #faf5ff;
            border: 1px solid #8B5CF6;
            border-radius: 8px;
            padding: 16px;
            margin: 24px 0;
        }
        .instructions h3 {
            color: #6D28D9;
            margin-bottom: 12px;
        }
        .instructions ol {
            margin-left: 20px;
        }
        .instructions li {
            margin-bottom: 8px;
            color: #1f2937;
        }
        .download-section {
            margin-top: 32px;
            padding: 24px;
            background: #f0fdf4;
            border: 1px solid #22c55e;
            border-radius: 8px;
        }
        .download-section h3 {
            color: #15803d;
            margin-bottom: 16px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #8B5CF6;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 8px;
            font-weight: 500;
        }
        .btn:hover {
            background: #7C3AED;
        }
        .btn-secondary {
            background: #6b7280;
        }
        .btn-secondary:hover {
            background: #4b5563;
        }
        .recording-variant {
            margin-top: 24px;
            padding: 16px;
            background: #fef2f2;
            border: 1px solid #DC2626;
            border-radius: 8px;
        }
        .recording-variant h3 {
            color: #991B1B;
            margin-bottom: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Skripter Icon Generator</h1>
        
        <p>This tool helps you generate the required PNG icons for the Skripter Chrome/Edge extension with your custom "S" design.</p>
        
        <div class="icon-preview">
            <div class="icon-size">
                <h3>16x16</h3>
                <div class="icon-display">
                    <svg width="16" height="16" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="64" cy="64" r="60" fill="#8B5CF6" stroke="#7C3AED" stroke-width="4"/>
                        <path d="M40 35C40 35 45 30 55 30C65 30 70 35 70 40C70 45 65 50 55 50C45 50 40 55 40 60C40 65 45 70 55 70C65 70 70 65 70 60" 
                              stroke="white" 
                              stroke-width="8" 
                              stroke-linecap="round" 
                              stroke-linejoin="round" 
                              fill="none"/>
                    </svg>
                </div>
                <p>Toolbar icon</p>
            </div>
            
            <div class="icon-size">
                <h3>32x32</h3>
                <div class="icon-display">
                    <svg width="32" height="32" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="64" cy="64" r="60" fill="#8B5CF6" stroke="#7C3AED" stroke-width="4"/>
                        <path d="M40 35C40 35 45 30 55 30C65 30 70 35 70 40C70 45 65 50 55 50C45 50 40 55 40 60C40 65 45 70 55 70C65 70 70 65 70 60" 
                              stroke="white" 
                              stroke-width="8" 
                              stroke-linecap="round" 
                              stroke-linejoin="round" 
                              fill="none"/>
                    </svg>
                </div>
                <p>Extension management</p>
            </div>
            
            <div class="icon-size">
                <h3>48x48</h3>
                <div class="icon-display">
                    <svg width="48" height="48" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="64" cy="64" r="60" fill="#8B5CF6" stroke="#7C3AED" stroke-width="4"/>
                        <path d="M40 35C40 35 45 30 55 30C65 30 70 35 70 40C70 45 65 50 55 50C45 50 40 55 40 60C40 65 45 70 55 70C65 70 70 65 70 60" 
                              stroke="white" 
                              stroke-width="8" 
                              stroke-linecap="round" 
                              stroke-linejoin="round" 
                              fill="none"/>
                    </svg>
                </div>
                <p>Extension details</p>
            </div>
            
            <div class="icon-size">
                <h3>128x128</h3>
                <div class="icon-display">
                    <svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="64" cy="64" r="60" fill="#8B5CF6" stroke="#7C3AED" stroke-width="4"/>
                        <path d="M40 35C40 35 45 30 55 30C65 30 70 35 70 40C70 45 65 50 55 50C45 50 40 55 40 60C40 65 45 70 55 70C65 70 70 65 70 60" 
                              stroke="white" 
                              stroke-width="8" 
                              stroke-linecap="round" 
                              stroke-linejoin="round" 
                              fill="none"/>
                    </svg>
                </div>
                <p>Chrome Web Store</p>
            </div>
        </div>

        <div class="recording-variant">
            <h3>🔴 Recording State Icon</h3>
            <div class="icon-display">
                <svg width="32" height="32" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="64" cy="64" r="60" fill="#DC2626" stroke="#B91C1C" stroke-width="4"/>
                    <path d="M40 35C40 35 45 30 55 30C65 30 70 35 70 40C70 45 65 50 55 50C45 50 40 55 40 60C40 65 45 70 55 70C65 70 70 65 70 60" 
                          stroke="white" 
                          stroke-width="8" 
                          stroke-linecap="round" 
                          stroke-linejoin="round" 
                          fill="none"/>
                    <circle cx="64" cy="64" r="8" fill="white" opacity="0.8"/>
                </svg>
            </div>
            <p>Red variant with recording indicator dot</p>
        </div>

        <div class="instructions">
            <h3>📋 How to Generate PNG Icons</h3>
            <ol>
                <li><strong>Option 1 - Using ImageMagick (Recommended):</strong>
                    <ul>
                        <li>Install ImageMagick: <code>brew install imagemagick</code> (macOS) or <code>sudo apt-get install imagemagick</code> (Ubuntu)</li>
                        <li>Run the script: <code>./generate-icons.sh</code></li>
                    </ul>
                </li>
                <li><strong>Option 2 - Online Tools:</strong>
                    <ul>
                        <li>Use online SVG to PNG converters like CloudConvert, Convertio, or SVGOMG</li>
                        <li>Upload the <code>icons/icon.svg</code> file</li>
                        <li>Convert to the required sizes: 16x16, 32x32, 48x48, 128x128</li>
                    </ul>
                </li>
                <li><strong>Option 3 - Design Software:</strong>
                    <ul>
                        <li>Open the SVG in Figma, Sketch, or Adobe Illustrator</li>
                        <li>Export as PNG in the required sizes</li>
                    </ul>
                </li>
            </ol>
        </div>

        <div class="download-section">
            <h3>📁 Required Icon Files</h3>
            <p>Your extension needs these PNG files in the <code>icons/</code> directory:</p>
            <ul>
                <li><code>icon16.png</code> (16x16) - Extension toolbar</li>
                <li><code>icon32.png</code> (32x32) - Extension management</li>
                <li><code>icon48.png</code> (48x48) - Extension details</li>
                <li><code>icon128.png</code> (128x128) - Chrome Web Store</li>
                <li><code>icon-recording.png</code> (32x32, red variant) - Recording state</li>
            </ul>
        </div>

        <div style="margin-top: 32px;">
            <a href="icons/icon.svg" download class="btn">📥 Download Main SVG Icon</a>
            <a href="icons/icon-recording.svg" download class="btn">📥 Download Recording SVG Icon</a>
            <a href="generate-icons.sh" download class="btn btn-secondary">📥 Download Generation Script</a>
        </div>
    </div>

    <script>
        // Add download functionality for the SVG
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎨 Skripter Icon Generator loaded');
            console.log('📱 Generate PNG icons from the SVG file');
        });
    </script>
</body>
</html> 