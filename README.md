# Skripter - Chrome/Edge Extension

A simple and intuitive Chrome/Edge extension for uploading, recording, and transcribing audio with features for generating summaries and reports. Perfect for Dutch-speaking users who need to transcribe meetings from Google Meet, Zoom, and Microsoft Teams.

## 🎯 Features

### Core Functionality
- **Audio Upload & Recording**: Support for MP3, WAV, M4A, and other common audio formats (up to 10GB)
- **Real-time Recording**: Built-in audio recording with play, pause/resume, and stop functionality
- **Meeting Integration**: Seamless integration with Google Meet, Zoom, and Microsoft Teams
- **Transcription**: Multi-language support (Dutch, English, German, Spanish)
- **Summaries & Reports**: Generate full transcriptions, summaries, and detailed reports
- **Export Options**: Export in DOCX and PDF formats

### User Management
- **User Registration**: Sign up with name, email, and password
- **Google Sign-in**: OAuth integration with Google accounts
- **Subscription Plans**: Free, Basis, and Teams tiers with different features
- **Workspace Creation**: Automatic workspace setup upon registration

### Organization Features
- **Folder Management**: Organize transcripts with folders and tags
- **Search Functionality**: Built-in search within transcripts
- **Dark Mode**: Optional dark theme support
- **Responsive Design**: Works on all screen sizes

## 📋 Subscription Plans

### Free Plan
- 3 recordings/month
- 1 hour max per recording
- Transcription and summaries only
- No PDF/DOCX downloads
- No reports

### Basis Plan
- Unlimited hours/month
- Connect with Google Meet, Zoom, and Teams
- Full transcriptions, summaries, and reports
- Folder management
- DOCX and PDF exports

### Teams Plan
- All Basis features
- Advanced folder/tag management
- Priority support
- Advanced analytics

## 🚀 Installation

### For Chrome/Edge Users

1. **Download the Extension**
   ```bash
   git clone https://github.com/your-repo/skripter-extension.git
   cd skripter-extension
   ```

2. **Load Extension in Chrome**
   - Open Chrome and go to `chrome://extensions/`
   - Enable "Developer mode" (toggle in top right)
   - Click "Load unpacked"
   - Select the extension folder

3. **Load Extension in Edge**
   - Open Edge and go to `edge://extensions/`
   - Enable "Developer mode" (toggle in bottom left)
   - Click "Load unpacked"
   - Select the extension folder

### For Developers

1. **Clone the Repository**
   ```bash
   git clone https://github.com/your-repo/skripter-extension.git
   cd skripter-extension
   ```

2. **Install Dependencies** (if any build process is added)
   ```bash
   npm install
   ```

3. **Build the Extension** (if needed)
   ```bash
   npm run build
   ```

4. **Load in Browser**
   - Follow the same steps as above for Chrome/Edge

## 🎮 Usage

### Getting Started

1. **Sign Up/Login**
   - Click the Skripter extension icon
   - Create an account or sign in with Google
   - Complete the onboarding process

2. **Recording Audio**
   - **In Extension**: Use the "Record" tab to record audio directly
   - **In Meetings**: Right-click on Google Meet/Zoom/Teams pages and select "Start Recording with Skripter"
   - **Upload Files**: Use the "Upload" tab to upload existing audio files

3. **Processing**
   - Select your preferred language (Dutch, English, German, Spanish)
   - Choose transcription quality (Quick or Accurate)
   - Wait for processing to complete

4. **Managing Transcripts**
   - View all transcripts in the "Transcripts" tab
   - Search through your transcripts
   - Organize with folders and tags
   - Export in DOCX or PDF format

### Meeting Platform Integration

#### Google Meet
- Extension automatically detects Google Meet pages
- Right-click to start/stop recording
- Recording indicator appears in the meeting interface

#### Zoom
- Works with Zoom web client
- Context menu integration for recording control
- Visual recording indicator

#### Microsoft Teams
- Seamless integration with Teams web interface
- Recording controls via context menu
- Real-time recording status

## 🛠️ Technical Details

### Architecture
- **Manifest V3**: Modern Chrome extension architecture
- **Content Scripts**: Integration with meeting platforms
- **Background Script**: Handles recording and API communication
- **Popup Interface**: User-friendly extension interface

### API Integration
- **Authentication**: JWT-based authentication
- **File Upload**: Multipart form data for audio files
- **Real-time Processing**: WebSocket support for live updates
- **Stripe Integration**: Secure payment processing

### Security Features
- **GDPR Compliance**: Full data protection compliance
- **Secure Storage**: Encrypted local storage
- **Permission Management**: Minimal required permissions
- **HTTPS Only**: All API calls use secure connections

## 🔧 Configuration

### Environment Variables
```bash
# API Configuration
API_BASE_URL=https://api.skripter.com
STRIPE_PUBLIC_KEY=your_stripe_public_key
GOOGLE_CLIENT_ID=your_google_client_id

# Feature Flags
ENABLE_DARK_MODE=true
ENABLE_GOOGLE_SIGNIN=true
ENABLE_MEETING_INTEGRATION=true
```

### Customization
- **Colors**: Modify `styles/popup.css` for theme changes
- **Languages**: Add new languages in the transcription settings
- **Platforms**: Extend meeting platform support in `content.js`

## 📁 File Structure

```
skripter-extension/
├── manifest.json          # Extension manifest
├── popup.html            # Main popup interface
├── background.js         # Background script
├── content.js           # Content script for meeting platforms
├── content.css          # Content script styles
├── styles/
│   └── popup.css       # Popup styles
├── scripts/
│   └── popup.js        # Popup functionality
├── icons/
│   ├── icon.svg        # Main icon
│   ├── icon16.png      # 16x16 icon
│   ├── icon32.png      # 32x32 icon
│   ├── icon48.png      # 48x48 icon
│   └── icon128.png     # 128x128 icon
└── README.md           # This file
```

## 🐛 Troubleshooting

### Common Issues

1. **Recording Not Working**
   - Check microphone permissions
   - Ensure HTTPS connection
   - Try refreshing the page

2. **Extension Not Loading**
   - Verify manifest.json syntax
   - Check browser console for errors
   - Ensure all files are present

3. **Meeting Integration Issues**
   - Refresh the meeting page
   - Check if the meeting platform is supported
   - Verify content script injection

4. **Authentication Problems**
   - Clear browser storage
   - Check network connectivity
   - Verify API endpoints

### Debug Mode
Enable debug logging by adding to `background.js`:
```javascript
const DEBUG = true;
if (DEBUG) console.log('Debug message');
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow the existing code style
- Add comments for complex logic
- Test on multiple browsers
- Ensure accessibility compliance
- Update documentation as needed

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs.skripter.com](https://docs.skripter.com)
- **Issues**: [GitHub Issues](https://github.com/your-repo/skripter-extension/issues)
- **Email**: <EMAIL>
- **Discord**: [Skripter Community](https://discord.gg/skripter)

## 🔄 Changelog

### Version 1.0.0
- Initial release
- Basic recording functionality
- Meeting platform integration
- User authentication
- Subscription management

### Upcoming Features
- Cloud storage integration (Google Drive, Dropbox)
- Advanced analytics dashboard
- Team collaboration features
- Mobile app companion
- API for third-party integrations

---

**Made with ❤️ for Dutch-speaking professionals** 