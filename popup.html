<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Skripter</title>
    <link rel="stylesheet" href="styles/popup.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <img src="icons/icon32.png" alt="Skripter" class="logo-icon">
                <h1>Skripter</h1>
            </div>
            <button class="settings-btn" id="settingsBtn">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/>
                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1Z"/>
                </svg>
            </button>
        </header>

        <!-- Auth Section -->
        <div id="authSection" class="auth-section">
            <div class="auth-container">
                <h2>Welcome to Skripter</h2>
                <p>Sign in to start transcribing your audio</p>
                
                <form id="loginForm" class="auth-form">
                    <div class="form-group">
                        <input type="email" id="email" placeholder="Email" required>
                    </div>
                    <div class="form-group">
                        <input type="password" id="password" placeholder="Password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Sign In</button>
                </form>
                
                <div class="divider">
                    <span>or</span>
                </div>
                
                <button id="googleSignIn" class="btn btn-google">
                    <svg width="20" height="20" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    Sign in with Google
                </button>
                
                <p class="auth-footer">
                    Don't have an account? <a href="#" id="showRegister">Sign up</a>
                </p>
            </div>
        </div>

        <!-- Register Section -->
        <div id="registerSection" class="auth-section hidden">
            <div class="auth-container">
                <h2>Create Account</h2>
                <p>Join Skripter to start transcribing</p>
                
                <form id="registerForm" class="auth-form">
                    <div class="form-group">
                        <input type="text" id="regName" placeholder="Full Name" required>
                    </div>
                    <div class="form-group">
                        <input type="email" id="regEmail" placeholder="Email" required>
                    </div>
                    <div class="form-group">
                        <input type="password" id="regPassword" placeholder="Password" required>
                    </div>
                    <div class="form-group">
                        <input type="password" id="regConfirmPassword" placeholder="Confirm Password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Create Account</button>
                </form>
                
                <p class="auth-footer">
                    Already have an account? <a href="#" id="showLogin">Sign in</a>
                </p>
            </div>
        </div>

        <!-- Main App Section -->
        <div id="mainApp" class="main-app hidden">
            <!-- Navigation -->
            <nav class="nav">
                <button class="nav-btn active" data-tab="record">Record</button>
                <button class="nav-btn" data-tab="upload">Upload</button>
                <button class="nav-btn" data-tab="transcripts">Transcripts</button>
            </nav>

            <!-- Record Tab -->
            <div id="recordTab" class="tab-content active">
                <div class="record-container">
                    <div class="record-status">
                        <div class="status-indicator" id="statusIndicator"></div>
                        <span id="statusText">Ready to record</span>
                    </div>
                    
                    <div class="record-controls">
                        <button id="recordBtn" class="btn btn-record">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                                <circle cx="12" cy="12" r="3"/>
                            </svg>
                            Start Recording
                        </button>
                        
                        <div class="recording-controls hidden" id="recordingControls">
                            <button id="pauseBtn" class="btn btn-secondary">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="6" y="4" width="4" height="16"/>
                                    <rect x="14" y="4" width="4" height="16"/>
                                </svg>
                                Pause
                            </button>
                            <button id="stopBtn" class="btn btn-danger">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="6" y="6" width="12" height="12"/>
                                </svg>
                                Stop
                            </button>
                        </div>
                    </div>
                    
                    <div class="recording-info hidden" id="recordingInfo">
                        <div class="timer" id="timer">00:00</div>
                        <div class="audio-visualizer" id="audioVisualizer"></div>
                    </div>
                </div>
            </div>

            <!-- Upload Tab -->
            <div id="uploadTab" class="tab-content">
                <div class="upload-container">
                    <div class="upload-area" id="uploadArea">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                            <polyline points="7,10 12,15 17,10"/>
                            <line x1="12" y1="15" x2="12" y2="3"/>
                        </svg>
                        <h3>Upload Audio File</h3>
                        <p>Drag and drop or click to select</p>
                        <p class="file-info">Supported: MP3, WAV, M4A (Max 10GB)</p>
                        <input type="file" id="fileInput" accept=".mp3,.wav,.m4a,.aac,.flac" hidden>
                    </div>
                    
                    <div class="upload-progress hidden" id="uploadProgress">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <span id="progressText">Uploading... 0%</span>
                    </div>
                </div>
            </div>

            <!-- Transcripts Tab -->
            <div id="transcriptsTab" class="tab-content">
                <div class="transcripts-container">
                    <div class="transcripts-header">
                        <h3>My Transcripts</h3>
                        <div class="search-box">
                            <input type="text" id="searchTranscripts" placeholder="Search transcripts...">
                        </div>
                    </div>
                    
                    <div class="transcripts-list" id="transcriptsList">
                        <!-- Transcripts will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Processing Modal -->
        <div id="processingModal" class="modal hidden">
            <div class="modal-content">
                <div class="processing-spinner"></div>
                <h3 id="processingTitle">Processing Audio</h3>
                <p id="processingText">Please wait while we transcribe your audio...</p>
                <div class="progress-bar">
                    <div class="progress-fill" id="modalProgressFill"></div>
                </div>
            </div>
        </div>

        <!-- Settings Modal -->
        <div id="settingsModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Settings</h3>
                    <button class="close-btn" id="closeSettings">×</button>
                </div>
                <div class="settings-content">
                    <div class="setting-group">
                        <label>Transcription Language</label>
                        <select id="transcriptionLanguage">
                            <option value="nl">Dutch</option>
                            <option value="en">English</option>
                            <option value="de">German</option>
                            <option value="es">Spanish</option>
                        </select>
                    </div>
                    <div class="setting-group">
                        <label>Transcription Quality</label>
                        <select id="transcriptionQuality">
                            <option value="quick">Quick</option>
                            <option value="accurate">Accurate</option>
                        </select>
                    </div>
                    <div class="setting-group">
                        <label>Theme</label>
                        <select id="themeSelect">
                            <option value="light">Light</option>
                            <option value="dark">Dark</option>
                        </select>
                    </div>
                    <div class="setting-group">
                        <button id="logoutBtn" class="btn btn-secondary">Sign Out</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="scripts/popup.js"></script>
</body>
</html> 