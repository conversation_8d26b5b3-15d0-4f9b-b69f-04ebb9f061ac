// Skripter Extension - Background Script
class SkripterBackground {
    constructor() {
        this.isRecording = false;
        this.currentTab = null;
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.recordingStartTime = null;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupContextMenus();
        this.checkForMeetingPlatforms();
    }

    setupEventListeners() {
        // Handle extension icon click
        chrome.action.onClicked.addListener((tab) => {
            this.handleExtensionClick(tab);
        });

        // Handle messages from popup and content scripts
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });

        // Handle tab updates to detect meeting platforms
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete') {
                this.checkForMeetingPlatforms();
            }
        });

        // Handle tab activation
        chrome.tabs.onActivated.addListener((activeInfo) => {
            this.checkForMeetingPlatforms();
        });

        // Handle storage changes
        chrome.storage.onChanged.addListener((changes, namespace) => {
            this.handleStorageChanges(changes, namespace);
        });
    }

    setupContextMenus() {
        // Create context menu for meeting platforms
        chrome.contextMenus.create({
            id: 'skripter-record',
            title: 'Start Recording with Skripter',
            contexts: ['page'],
            documentUrlPatterns: [
                'https://meet.google.com/*',
                'https://zoom.us/*',
                'https://teams.microsoft.com/*'
            ]
        });

        chrome.contextMenus.create({
            id: 'skripter-stop',
            title: 'Stop Recording',
            contexts: ['page'],
            documentUrlPatterns: [
                'https://meet.google.com/*',
                'https://zoom.us/*',
                'https://teams.microsoft.com/*'
            ]
        });

        // Handle context menu clicks
        chrome.contextMenus.onClicked.addListener((info, tab) => {
            this.handleContextMenuClick(info, tab);
        });
    }

    async handleExtensionClick(tab) {
        // Open popup when extension icon is clicked
        // The popup is handled by manifest.json action.default_popup
    }

    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action || message.type) {
                case 'startRecording':
                case 'START_RECORDING':
                    const recordingResult = await this.startTabRecording(sender.tab);
                    sendResponse(recordingResult);
                    break;

                case 'stopRecording':
                case 'STOP_RECORDING':
                    await this.stopRecording();
                    sendResponse({ success: true });
                    break;

                case 'GET_RECORDING_STATUS':
                    sendResponse({
                        isRecording: this.isRecording,
                        recordingTime: this.getRecordingTime()
                    });
                    break;

                case 'UPLOAD_AUDIO':
                    await this.uploadAudio(message.audioBlob, message.metadata);
                    sendResponse({ success: true });
                    break;

                case 'GET_USER_INFO':
                    const user = await this.getStoredUser();
                    sendResponse({ user });
                    break;

                case 'SHOW_NOTIFICATION':
                    this.showNotification(message.title, message.message);
                    sendResponse({ success: true });
                    break;

                default:
                    sendResponse({ error: 'Unknown message type' });
            }
        } catch (error) {
            console.error('Background message handling error:', error);
            sendResponse({ error: error.message });
        }
    }

    async startTabRecording(tab) {
        try {
            // For popup-initiated recording, we need to use tab capture
            // This is a simplified version that returns success for now
            // In a real implementation, you would use chrome.tabCapture API
            console.log('Starting tab recording for:', tab?.url);

            // Check if we can access the tab
            if (!tab || !tab.id) {
                return { success: false, error: 'No active tab found' };
            }

            // For now, return success and let the popup handle the actual recording
            // This is because getUserMedia works better in popup context
            return { success: false, error: 'Use popup recording instead' };

        } catch (error) {
            console.error('Error starting tab recording:', error);
            return { success: false, error: error.message };
        }
    }

    async startRecording(tab) {
        try {
            if (this.isRecording) {
                throw new Error('Already recording');
            }

            // Don't try to access media APIs in service worker
            // Just track recording state and let popup handle media
            this.isRecording = true;
            this.currentTab = tab;
            this.recordingStartTime = Date.now();
    
            // Update extension icon to show recording state
            this.updateExtensionIcon(true);
    
            // Send message to content script to update UI
            if (tab) {
                chrome.tabs.sendMessage(tab.id, {
                    type: 'RECORDING_STARTED',
                    recordingTime: this.getRecordingTime()
                }).catch(() => {
                    // Content script might not be ready
                });
            }
    
            // Show notification
            this.showNotification('Recording Started', 'Skripter is now recording.');
    
        } catch (error) {
            console.error('Error starting recording:', error);
            this.showNotification('Recording Error', 'Could not start recording.');
            throw error;
        }
    }

    async stopRecording() {
        if (!this.isRecording) {
            return;
        }

        try {
            this.isRecording = false;
    
            // Update extension icon
            this.updateExtensionIcon(false);
    
            // Send message to content script
            if (this.currentTab) {
                chrome.tabs.sendMessage(this.currentTab.id, {
                    type: 'RECORDING_STOPPED'
                }).catch(() => {
                    // Content script might not be ready
                });
            }
    
            this.showNotification('Recording Stopped', 'Recording has been stopped.');
    
        } catch (error) {
            console.error('Error stopping recording:', error);
            this.showNotification('Recording Error', 'Error stopping recording.');
        }
    }

    async processRecording() {
        try {
            const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });
            
            // Get user info
            const user = await this.getStoredUser();
            if (!user || !user.token) {
                this.showNotification('Authentication Required', 'Please sign in to Skripter to save your recording.');
                return;
            }

            // Upload to server
            await this.uploadAudio(audioBlob, {
                source: 'meeting',
                platform: this.detectMeetingPlatform(this.currentTab?.url),
                duration: this.getRecordingTime(),
                timestamp: new Date().toISOString()
            });

            this.showNotification('Processing Complete', 'Your recording has been processed and saved.');

        } catch (error) {
            console.error('Error processing recording:', error);
            this.showNotification('Processing Error', 'Failed to process recording. Please try again.');
        }
    }

    async uploadAudio(audioBlob, metadata) {
        try {
            const user = await this.getStoredUser();
            if (!user || !user.token) {
                throw new Error('User not authenticated');
            }

            const formData = new FormData();
            formData.append('audio', audioBlob);
            formData.append('metadata', JSON.stringify(metadata));

            const response = await fetch('https://api.skripter.com/transcribe', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${user.token}`
                },
                body: formData
            });

            if (!response.ok) {
                throw new Error('Upload failed');
            }

            const result = await response.json();
            return result;

        } catch (error) {
            console.error('Upload error:', error);
            throw error;
        }
    }

    getRecordingTime() {
        if (!this.recordingStartTime) return 0;
        return Date.now() - this.recordingStartTime;
    }

    updateExtensionIcon(isRecording) {
        const iconPath = isRecording ? 'icons/icon-recording.png' : 'icons/icon32.png';
        chrome.action.setIcon({ path: iconPath });
        
        const title = isRecording ? 'Skripter - Recording...' : 'Skripter';
        chrome.action.setTitle({ title });
    }

    showNotification(title, message) {
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'icons/icon48.png',
            title: title,
            message: message
        });
    }

    checkForMeetingPlatforms() {
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (tabs.length > 0) {
                const tab = tabs[0];
                const isMeetingPlatform = this.isMeetingPlatform(tab.url);
                
                if (isMeetingPlatform) {
                    this.enableMeetingFeatures(tab);
                } else {
                    this.disableMeetingFeatures();
                }
            }
        });
    }

    isMeetingPlatform(url) {
        if (!url) return false;
        
        const meetingPatterns = [
            /^https:\/\/meet\.google\.com\//,
            /^https:\/\/zoom\.us\//,
            /^https:\/\/teams\.microsoft\.com\//
        ];

        return meetingPatterns.some(pattern => pattern.test(url));
    }

    detectMeetingPlatform(url) {
        if (!url) return 'unknown';
        
        if (url.includes('meet.google.com')) return 'google-meet';
        if (url.includes('zoom.us')) return 'zoom';
        if (url.includes('teams.microsoft.com')) return 'teams';
        
        return 'unknown';
    }

    enableMeetingFeatures(tab) {
        // Inject content script if not already injected
        chrome.scripting.executeScript({
            target: { tabId: tab.id },
            files: ['content.js']
        }).catch(() => {
            // Script might already be injected
        });

        // Update context menus
        chrome.contextMenus.update('skripter-record', { visible: true });
        chrome.contextMenus.update('skripter-stop', { visible: true });
    }

    disableMeetingFeatures() {
        // Hide context menus
        chrome.contextMenus.update('skripter-record', { visible: false });
        chrome.contextMenus.update('skripter-stop', { visible: false });
    }

    async handleContextMenuClick(info, tab) {
        try {
            if (info.menuItemId === 'skripter-record') {
                await this.startRecording(tab);
            } else if (info.menuItemId === 'skripter-stop') {
                await this.stopRecording();
            }
        } catch (error) {
            console.error('Context menu error:', error);
            this.showNotification('Error', 'Failed to perform action. Please try again.');
        }
    }

    async handleStorageChanges(changes, namespace) {
        if (namespace === 'sync' && changes.user) {
            // Handle user authentication changes
            const user = changes.user.newValue;
            if (!user) {
                // User logged out, stop recording if active
                if (this.isRecording) {
                    await this.stopRecording();
                }
            }
        }
    }

    async getStoredUser() {
        try {
            const result = await chrome.storage.sync.get(['user']);
            return result.user;
        } catch (error) {
            console.error('Error getting stored user:', error);
            return null;
        }
    }

    // Handle installation and updates
    handleInstall(details) {
        if (details.reason === 'install') {
            // First time installation
            this.showNotification('Welcome to Skripter!', 'Click the extension icon to get started.');
        } else if (details.reason === 'update') {
            // Extension updated
            console.log('Skripter extension updated');
        }
    }
}

// Initialize background script
const background = new SkripterBackground();

// Handle extension installation and updates
chrome.runtime.onInstalled.addListener((details) => {
    background.handleInstall(details);
});

// Handle extension startup
chrome.runtime.onStartup.addListener(() => {
    console.log('Skripter extension started');
});